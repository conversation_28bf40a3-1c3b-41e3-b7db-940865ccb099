@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    background: #0a0a0a;
  }
}

@layer utilities {
  .brand-card {
    background: rgba(30, 30, 30, 0.95);
    border: 1px solid rgba(59, 130, 246, 0.2);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  .brand-button {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  }
  
  .brand-button:hover {
    background: linear-gradient(135deg, #2563eb, #1e40af);
  }
  
  .brand-accent {
    color: #3b82f6;
  }
  
  .floating-animation {
    animation: float 6s ease-in-out infinite;
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
}
