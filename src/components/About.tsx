import React from 'react';
import { Users, Award, Globe, CheckCircle } from 'lucide-react';

const About = () => {
  const stats = [
    { icon: Users, label: 'Trusted Partners', value: '100+' },
    { icon: Award, label: 'Years Experience', value: '20+' },
    { icon: Globe, label: 'Cities Served', value: '50+' },
    { icon: CheckCircle, label: 'Projects Completed', value: '500+' }
  ];

  const usps = [
    {
      title: 'Extensive Network',
      description: 'Access to a carefully curated network of top-tier vendors and specialists across the country.'
    },
    {
      title: 'Full-Service Solutions',
      description: 'One point of contact for all your trade show needs - from design to storage and everything in between.'
    },
    {
      title: 'Unmatched Flexibility',
      description: 'Adaptable solutions that scale with your needs, whether you\'re a startup or Fortune 500 company.'
    },
    {
      title: 'Proven Results',
      description: 'Track record of delivering exceptional results on time and within budget for over two decades.'
    }
  ];

  return (
    <section id="about" className="py-20 bg-black relative">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/3 right-1/3 w-80 h-80 bg-orange-500 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 left-1/3 w-80 h-80 bg-green-500 rounded-full blur-3xl"></div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div>
            <h2 className="text-4xl font-bold text-white mb-6">
              Why Choose Form4Design?
            </h2>
            
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              With over 20 years in the trade show industry, Form4Design and Jason Moran have built 
              a reputation for delivering exceptional results through strategic partnerships and 
              unwavering commitment to client success.
            </p>

            <div className="space-y-6">
              {usps.map((usp, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="w-2 h-2 rounded-full bg-blue-500 mt-3 flex-shrink-0"></div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">{usp.title}</h3>
                    <p className="text-gray-300">{usp.description}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-8">
              <button 
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="brand-button text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
              >
                Let's Work Together
              </button>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 gap-6">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div
                  key={index}
                  className="brand-card rounded-xl p-6 text-center transition-all duration-300 group hover:transform hover:scale-105 hover:shadow-xl"
                >
                  <div className="w-12 h-12 mx-auto mb-4 rounded-lg bg-blue-600 flex items-center justify-center group-hover:scale-110 transition-transform">
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="text-3xl font-bold brand-accent mb-2">{stat.value}</div>
                  <div className="text-gray-300 text-sm">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Value Proposition */}
        <div className="mt-20 text-center">
          <div className="brand-card rounded-2xl p-8 lg:p-12">
            <h3 className="text-3xl font-bold text-white mb-6">
              The Form4Design Advantage
            </h3>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Unlike traditional agencies, Jason operates as a strategic broker, connecting you with 
              the best specialists for your specific needs. This model ensures you get top-tier 
              expertise at competitive prices, with the flexibility to scale services up or down 
              based on your project requirements.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;