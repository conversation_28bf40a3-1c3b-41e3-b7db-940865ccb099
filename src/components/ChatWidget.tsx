import React, { useState } from 'react';
import { MessageCircle, X, Send } from 'lucide-react';

const ChatWidget = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      // Here you would typically send the message to your chat system
      console.log('Chat message:', message);
      setMessage('');
      // For demo purposes, we'll just show an auto-response
      setTimeout(() => {
        alert('Thanks for your message! <PERSON> will respond shortly.');
      }, 500);
    }
  };

  return (
    <>
      {/* Chat Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-6 right-6 w-14 h-14 brand-button text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center z-50"
      >
        {isOpen ? <X className="w-6 h-6" /> : <MessageCircle className="w-6 h-6" />}
      </button>

      {/* Chat Window */}
      {isOpen && (
        <div className="fixed bottom-24 right-6 w-80 brand-card rounded-xl shadow-2xl z-50 overflow-hidden">
          {/* Header */}
          <div className="brand-button p-4">
            <h3 className="text-white font-semibold">Chat with Jason</h3>
            <p className="text-white/80 text-sm">Typically responds within minutes</p>
          </div>

          {/* Messages */}
          <div className="p-4 h-64 overflow-y-auto bg-black/50">
            <div className="bg-black/70 border border-blue-500/20 rounded-lg p-3 mb-3">
              <p className="text-white text-sm">
                Hi! I'm Jason Moran. How can I help you with your trade show needs today?
              </p>
              <span className="text-gray-400 text-xs">Just now</span>
            </div>
          </div>

          {/* Input */}
          <form onSubmit={handleSubmit} className="p-4 border-t border-blue-500/20">
            <div className="flex space-x-2">
              <input
                type="text"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Type your message..."
                className="flex-1 px-3 py-2 bg-black/50 border border-blue-500/30 rounded-lg text-white placeholder-gray-400 focus:border-blue-400 focus:ring-1 focus:ring-blue-400 transition-colors text-sm backdrop-blur-sm"
              />
              <button
                type="submit"
                className="brand-button text-white p-2 rounded-lg transition-all duration-300"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
          </form>
        </div>
      )}
    </>
  );
};

export default ChatWidget;